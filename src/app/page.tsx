'use client';

import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { HeroSection, FeaturesSection, ProcessSection, DigitalJourneySection, SMESection, CTASection } from '@/components/home';
import { StructuredData } from '@/components';
import { generateStructuredData } from '@/utils/seo';
import { FAQ_ITEMS } from '@/utils/constants';

// Register ScrollTrigger plugin
gsap.registerPlugin(ScrollTrigger);

export default function Home() {
  const containerRef = useRef<HTMLDivElement>(null);
  const sectionsRef = useRef<HTMLDivElement[]>([]);

  // Generate FAQ structured data
  const faqStructuredData = generateStructuredData.faq(
    FAQ_ITEMS.map(item => ({
      question: item.question,
      answer: item.answer
    }))
  );

  useEffect(() => {
    const container = containerRef.current;
    const sections = sectionsRef.current;

    if (!container || sections.length === 0) return;

    // Set up the container for full-screen scrolling
    gsap.set(container, {
      height: `${sections.length * 100}vh`
    });

    // Pin the container and create scroll-triggered animations
    ScrollTrigger.create({
      trigger: container,
      start: "top top",
      end: `+=${(sections.length - 1) * window.innerHeight}`,
      pin: true,
      scrub: 1,
      onUpdate: (self) => {
        const progress = self.progress;
        const sectionIndex = Math.floor(progress * sections.length);
        const sectionProgress = (progress * sections.length) % 1;

        // Hide all sections first
        sections.forEach((section, index) => {
          if (index === sectionIndex) {
            // Current section - fade in
            gsap.set(section, {
              opacity: 1 - sectionProgress,
              y: -sectionProgress * 100,
              zIndex: 10
            });
          } else if (index === sectionIndex + 1) {
            // Next section - fade in from bottom
            gsap.set(section, {
              opacity: sectionProgress,
              y: (1 - sectionProgress) * 100,
              zIndex: 9
            });
          } else {
            // Other sections - hide
            gsap.set(section, {
              opacity: 0,
              y: index < sectionIndex ? -100 : 100,
              zIndex: 1
            });
          }
        });
      }
    });

    // Initial setup - show only first section
    sections.forEach((section, index) => {
      gsap.set(section, {
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        opacity: index === 0 ? 1 : 0,
        y: index === 0 ? 0 : 100
      });
    });

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);

  const addToRefs = (el: HTMLDivElement | null) => {
    if (el && !sectionsRef.current.includes(el)) {
      sectionsRef.current.push(el);
    }
  };

  return (
    <>
      <StructuredData data={generateStructuredData.service()} />
      <StructuredData data={faqStructuredData} />

      <div ref={containerRef} className="relative overflow-hidden">
        <div ref={addToRefs} className="section-slide">
          <HeroSection />
        </div>
        <div ref={addToRefs} className="section-slide">
          <FeaturesSection />
        </div>
        <div ref={addToRefs} className="section-slide">
          <ProcessSection />
        </div>
        <div ref={addToRefs} className="section-slide">
          <DigitalJourneySection />
        </div>
        <div ref={addToRefs} className="section-slide">
          <SMESection />
        </div>
        <div ref={addToRefs} className="section-slide">
          <CTASection />
        </div>
      </div>
    </>
  );
}
